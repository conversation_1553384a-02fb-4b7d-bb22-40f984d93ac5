lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      base64-js:
        specifier: ^1.5.1
        version: 1.5.1
      crypto-js:
        specifier: ^4.2.0
        version: 4.2.0
      gm-crypt:
        specifier: ^0.0.2
        version: 0.0.2
      jsencrypt:
        specifier: ^3.3.2
        version: 3.3.2
      sm-crypto:
        specifier: ^0.3.13
        version: 0.3.13

packages:

  base64-js@1.5.1:
    resolution: {integrity: sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==}

  crypto-js@4.2.0:
    resolution: {integrity: sha512-KALDyEYgpY+Rlob/iriUtjV6d5Eq+Y191A5g4UqLAi8CyGP9N1+FdVbkc1SxKc2r4YAYqG8JzO2KGL+AizD70Q==}

  gm-crypt@0.0.2:
    resolution: {integrity: sha512-eg3EaPQYCwWI7UNMvV4ReHD4rR1mtegDDyK80nr49FKw3PgyvVCZ44wIeqA88ITvjIVC3mT/8ZZG0qvH701LzA==}

  jsbn@1.1.0:
    resolution: {integrity: sha512-4bYVV3aAMtDTTu4+xsDYa6sy9GyJ69/amsu9sYF2zqjiEoZA5xJi3BrfX3uY+/IekIu7MwdObdbDWpoZdBv3/A==}

  jsencrypt@3.3.2:
    resolution: {integrity: sha512-arQR1R1ESGdAxY7ZheWr12wCaF2yF47v5qpB76TtV64H1pyGudk9Hvw8Y9tb/FiTIaaTRUyaSnm5T/Y53Ghm/A==}

  sm-crypto@0.3.13:
    resolution: {integrity: sha512-ztNF+pZq6viCPMA1A6KKu3bgpkmYti5avykRHbcFIdSipFdkVmfUw2CnpM2kBJyppIalqvczLNM3wR8OQ0pT5w==}

snapshots:

  base64-js@1.5.1: {}

  crypto-js@4.2.0: {}

  gm-crypt@0.0.2:
    dependencies:
      base64-js: 1.5.1

  jsbn@1.1.0: {}

  jsencrypt@3.3.2: {}

  sm-crypto@0.3.13:
    dependencies:
      jsbn: 1.1.0
