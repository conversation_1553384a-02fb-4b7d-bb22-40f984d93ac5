/*
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-08-17 12:41:52
 * @LastEditTime: 2025-08-17 13:18:15
 * @FilePath: /逆向百例/广西外国语学院教务管理/run.js
 */

const https = require('https');

// 加载 RSA 相关函数
eval(require('fs').readFileSync(require.resolve('./loader'), 'utf8'));

// 从接口获取公钥信息
function getPublicKey(callback) {
    const now = Date.now();
    const url = `https://jwxt.gxufl.com/xtgl/login_getPublicKey.html?time=${now}&_=${now - 1}`;

    https.get(url, (res) => {
        let data = '';

        res.on('data', (chunk) => {
            data += chunk;
        });

        res.on('end', () => {
            try {
                const publicKeyData = JSON.parse(data);
                callback(null, publicKeyData);
            } catch (error) {
                callback(error, null);
            }
        });
    }).on('error', (error) => {
        callback(error, null);
    });
}

// 主逻辑：获取公钥并加密密码
getPublicKey((error, publicKeyData) => {
    if (error) {
        console.error('获取公钥失败:', error);
        return;
    }

    const { modulus, exponent } = publicKeyData;
    const pass = '654321';

    var rsaKey = new RSAKey();
    rsaKey.setPublic(b64tohex(modulus), b64tohex(exponent));
    var enPassword = hex2b64(rsaKey.encrypt(pass));
    console.log(enPassword);
});