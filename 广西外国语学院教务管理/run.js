/*
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-08-17 12:41:52
 * @LastEditTime: 2025-08-17 13:16:28
 * @FilePath: /逆向百例/广西外国语学院教务管理/run.js
 */
eval(require('fs').readFileSync(require.resolve('./loader'), 'utf8'));
    let pass='654321'
	var rsaKey = new RSAKey();
				rsaKey.setPublic(b64tohex(modulus), b64tohex("AQAB"));
				var enPassword = hex2b64(rsaKey.encrypt(pass));
                console.log(enPassword)